# File Status Configuration System

This document describes the unified file status configuration system that provides consistent status handling across both dropdown lists and data tables in the application.

## Overview

The file status system consists of:
- **FileStatus enum**: Defines the four file status types
- **FileStatusConfig interface**: Comprehensive configuration for each status
- **Helper functions**: Utilities for getting status configurations and options
- **Reusable components**: FileStatus and FileStatusSelect components

## File Status Types

```typescript
enum FileStatus {
    Open = 0,                           // File is open and being processed (default)
    ClosedAndReadyToLinkedToBox = 1,    // File is closed and ready to be linked to Box
    ClosedAndLinkedToBox = 2,           // File is closed and linked to a box
    ClosedAndExported = 3,              // File is closed and exported
}
```

## Status Configuration

Each status includes:
- **value**: Numeric value (0-3)
- **label**: Translated display name
- **description**: Human-readable description
- **className**: Tailwind CSS classes for styling
- **icon**: Icon identifier (for future use)
- **color**: Base color scheme

### Color Schemes
- **Open**: Blue (processing state)
- **Ready to Link**: Yellow (pending action)
- **Linked to Box**: Green (completed action)
- **Exported**: Purple (final state)

## Translation Keys

File status translations are located under `nav.files.status.*`:

```json
{
  "nav": {
    "files": {
      "status": {
        "open": "Open / مفتوح",
        "closedAndReadyToLinkedToBox": "Closed & Ready to Link / مغلق وجاهز للربط",
        "closedAndLinkedToBox": "Closed & Linked to Box / مغلق ومربوط بصندوق",
        "closedAndExported": "Closed & Exported / مغلق ومُصدر"
      }
    }
  }
}
```

## Components

### FileStatus Component

Displays file status with multiple variants:

```typescript
<FileStatus 
    status={fileStatus} 
    variant="badge" // 'badge' | 'text' | 'full'
    showIcon={false}
    className="custom-class"
/>
```

**Variants:**
- `badge`: Colored badge with border (default)
- `text`: Simple text display
- `full`: Badge + description

### FileStatusSelect Component

Dropdown for status selection:

```typescript
<FileStatusSelect
    value={selectedStatus}
    onChange={handleStatusChange}
    includeAll={true}
    variant="default" // 'default' | 'linking' | 'export'
    placeholder="Select status..."
    className="w-64"
/>
```

**Variants:**
- `default`: All status options
- `linking`: Only statuses relevant for linking (ClosedAndReadyToLinkedToBox)
- `export`: Only statuses relevant for export (ClosedAndLinkedToBox)

## Helper Functions

### getFileStatusConfig(t)
Returns complete status configuration object with translations.

### getFileStatusOptions(t, includeAll?)
Returns status options formatted for dropdown components.

### getFileStatusByValue(status, t)
Returns configuration for a specific status value.

### getFileStatusOptionsForLinking(t)
Returns filtered options for linking workflows.

### getFileStatusOptionsForExport(t)
Returns filtered options for export workflows.

## Usage Examples

### In Data Tables
```typescript
{
    header: t('nav.shared.status'),
    accessorKey: 'fileStatus',
    cell: ({ row }) => (
        <FileStatus 
            status={row.original.fileStatus} 
            variant="badge" 
        />
    ),
}
```

### In Filter Forms
```typescript
<Controller
    name="fileStatus"
    control={control}
    render={({ field }) => (
        <FileStatusSelect
            value={field.value || ''}
            onChange={field.onChange}
            includeAll={true}
        />
    )}
/>
```

### Custom Implementation
```typescript
const { t } = useTranslation()
const statusConfig = getFileStatusConfig(t)
const currentStatus = statusConfig[FileStatus.Open]

// Use currentStatus.label, currentStatus.className, etc.
```

## Migration from Legacy System

The old file status system in `src/views/digitization/Daily/types.ts` has been replaced. Key changes:

1. **Enum values**: Now supports 4 statuses (0-3) instead of 2 (0-1)
2. **Configuration**: Centralized in `src/types/fileStatus.ts`
3. **Components**: Replaced hardcoded status handling with reusable components
4. **Translations**: Moved to `nav.files.status.*` namespace

## Files Updated

- `src/types/fileStatus.ts` - Core configuration
- `src/components/shared/FileStatus.tsx` - Display component
- `src/components/shared/FileStatusSelect.tsx` - Selection component
- `src/views/digitization/Daily/components/DailyListTable.tsx` - Updated table
- `src/views/digitization/Daily/components/DailyTableFilter.tsx` - Updated filter
- `src/locales/lang/en.json` - English translations
- `src/locales/lang/ar.json` - Arabic translations

## Best Practices

1. **Always use the FileStatus component** for displaying status in tables
2. **Use FileStatusSelect** for status selection in forms and filters
3. **Use appropriate variants** based on context (linking, export, etc.)
4. **Include translations** for all status-related text
5. **Follow the established color schemes** for consistency
6. **Use helper functions** instead of hardcoding status logic

## Testing

The system includes comprehensive examples in `src/components/shared/FileStatusExamples.tsx` that demonstrate all variants and use cases.

import React, { useState, useEffect, useRef } from 'react'
import { Card, Button, Input, Select } from '@/components/ui'
import {
    TbEdit,
    Tb<PERSON><PERSON>ck,
    TbX,
    TbCalendar,
    TbBuilding,
    TbNotes,
    TbTag,
    TbSearch,
} from 'react-icons/tb'
import { DocumentDetails, DocumentUpdate } from '@/@types/document'
import { useUpdateDocument } from '@/hooks/documents'
import { useGetOrgsStructure } from '@/hooks/orgs'
import { useKeywordSearch } from '@/hooks/keywords'

interface UpdateDocumentDataProps {
    document: DocumentDetails | null
    onUpdateSuccess?: () => void
}

export default function UpdateDocumentData({
    document,
    onUpdateSuccess,
}: UpdateDocumentDataProps) {
    const [isEditing, setIsEditing] = useState(false)
    const [formData, setFormData] = useState<DocumentUpdate>({
        organizationalNodeId: '',
        followUpDate: '',
        notes: '',
        keywordIds: [],
    })
    const [keywordSearchTerm, setKeywordSearchTerm] = useState('')
    const [showSearchDropdown, setShowSearchDropdown] = useState(false)
    const [selectedKeywords, setSelectedKeywords] = useState<
        Array<{ id: string; value: string }>
    >([])

    const searchInputRef = useRef<HTMLInputElement>(null)
    const dropdownRef = useRef<HTMLDivElement>(null)

    const updateDocumentMutation = useUpdateDocument()
    const { flattenedNodes } = useGetOrgsStructure()
    const {
        results: searchResults,
        isLoading: isSearchLoading,
        search,
    } = useKeywordSearch()

    const nodesOptions = flattenedNodes.map((node) => ({
        value: node.code,
        label: `${node.name} (${node.code})`,
    }))

    // Add empty option as first choice
    const selectOptions = [
        { value: '', label: 'organization...' },
        ...nodesOptions,
    ]

    useEffect(() => {
        if (document) {
            setFormData({
                organizationalNodeId: document.organizationalNodeId || '',
                followUpDate: document.followUpDate || '',
                notes: document.notes || '',
                keywordIds: document.keywords?.map((k) => k.id) || [],
            })
            // Initialize selected keywords from document
            const docKeywords =
                document.keywords?.map((k) => ({
                    id: k.id.toString(),
                    value: k.value,
                })) || []
            setSelectedKeywords(docKeywords)
        }
    }, [document])

    // Handle click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node) &&
                searchInputRef.current &&
                !searchInputRef.current.contains(event.target as Node)
            ) {
                setShowSearchDropdown(false)
            }
        }

        window.addEventListener('mousedown', handleClickOutside)
        return () => {
            window.removeEventListener('mousedown', handleClickOutside)
        }
    }, [])

    const handleInputChange = (
        field: keyof DocumentUpdate,
        value: string | number[],
    ) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }))
    }

    const handleKeywordSearch = (searchTerm: string) => {
        setKeywordSearchTerm(searchTerm)
        if (searchTerm.trim()) {
            search(searchTerm)
            setShowSearchDropdown(true)
        } else {
            setShowSearchDropdown(false)
        }
    }

    const handleKeywordSelect = (keyword: { id: string; value: string }) => {
        const isAlreadySelected = selectedKeywords.some(
            (k) => k.id === keyword.id,
        )
        if (!isAlreadySelected) {
            const newSelectedKeywords = [...selectedKeywords, keyword]
            setSelectedKeywords(newSelectedKeywords)

            // Update form data with keyword IDs
            const keywordIds = newSelectedKeywords.map((k) => parseInt(k.id))
            handleInputChange('keywordIds', keywordIds)
        }
        setKeywordSearchTerm('')
        setShowSearchDropdown(false)
    }

    const handleKeywordRemove = (keywordId: string) => {
        const newSelectedKeywords = selectedKeywords.filter(
            (k) => k.id !== keywordId,
        )
        setSelectedKeywords(newSelectedKeywords)

        // Update form data with keyword IDs
        const keywordIds = newSelectedKeywords.map((k) => parseInt(k.id))
        handleInputChange('keywordIds', keywordIds)
    }

    const handleSave = async () => {
        if (!document) return

        try {
            const updateData: DocumentUpdate = {
                ...formData,
                followUpDate: formData.followUpDate
                    ? new Date(formData.followUpDate).toISOString()
                    : '',
            }

            await updateDocumentMutation.mutateAsync({
                documentId: document?.documentId,
                document: updateData,
            })

            setIsEditing(false)
            onUpdateSuccess?.()
        } catch (error) {
            console.error('Update failed:', error)
        }
    }

    const handleCancel = () => {
        if (document) {
            setFormData({
                organizationalNodeId: document.organizationalNodeId || '',
                followUpDate: document.followUpDate || '',
                notes: document.notes || '',
                keywordIds: document.keywords?.map((k) => k.id) || [],
            })
            // Reset selected keywords to document keywords
            const docKeywords =
                document.keywords?.map((k) => ({
                    id: k.id.toString(),
                    value: k.value,
                })) || []
            setSelectedKeywords(docKeywords)
        }
        setKeywordSearchTerm('')
        setShowSearchDropdown(false)
        setIsEditing(false)
    }

    const formatDate = (dateString: string) => {
        if (!dateString) return '-'
        return new Date(dateString).toLocaleDateString()
    }

    if (!document) {
        return (
            <Card className="p-6">
                <div className="text-center text-gray-500">
                    <TbEdit className="mx-auto text-4xl mb-2" />
                    <p>No document selected</p>
                </div>
            </Card>
        )
    }

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    Update Data
                </h3>
                {!isEditing && (
                    <Button
                        variant="default"
                        size="xs"
                        onClick={() => setIsEditing(true)}
                    >
                        <TbEdit className="w-4 h-4 mr-2" />
                    </Button>
                )}
            </div>

            <div className="space-y-6">
                {/* Organizational Node */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbBuilding className="w-4 h-4 mr-2" />
                        Organizational Node ID
                    </label>
                    {isEditing ? (
                        <Select
                            value={selectOptions.find(
                                (option) =>
                                    option.value ===
                                    formData.organizationalNodeId,
                            )}
                            options={selectOptions}
                            placeholder="Select an organization..."
                            onChange={(selectedOption) =>
                                handleInputChange(
                                    'organizationalNodeId',
                                    selectedOption?.value || '',
                                )
                            }
                            isClearable
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-gray-900">
                                {(() => {
                                    const selectedNode = flattenedNodes.find(
                                        (node) =>
                                            node.code ===
                                            formData.organizationalNodeId,
                                    )
                                    return selectedNode
                                        ? `${selectedNode.name} (${selectedNode.code})`
                                        : formData.organizationalNodeId || '-'
                                })()}
                            </p>
                        </div>
                    )}
                </div>

                {/* Follow Up Date */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbCalendar className="w-4 h-4 mr-2" />
                        Follow Up Date
                    </label>
                    {isEditing ? (
                        <Input
                            type="date"
                            value={
                                formData.followUpDate
                                    ? formData.followUpDate.split('T')[0]
                                    : ''
                            }
                            onChange={(e) =>
                                handleInputChange(
                                    'followUpDate',
                                    e.target.value,
                                )
                            }
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-gray-900">
                                {formatDate(formData.followUpDate)}
                            </p>
                        </div>
                    )}
                </div>

                {/* Notes */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbNotes className="w-4 h-4 mr-2" />
                        Notes
                    </label>
                    {isEditing ? (
                        <textarea
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical min-h-[100px]"
                            value={formData.notes}
                            placeholder="Enter notes..."
                            rows={4}
                            onChange={(e) =>
                                handleInputChange('notes', e.target.value)
                            }
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg min-h-[100px]">
                            <p className="text-gray-900 whitespace-pre-wrap">
                                {formData.notes || '-'}
                            </p>
                        </div>
                    )}
                </div>

                {/* Keywords */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbTag className="w-4 h-4 mr-2" />
                        Keywords
                    </label>
                    {isEditing ? (
                        <div className="space-y-2">
                            {/* Selected Keywords Display */}
                            {selectedKeywords.length > 0 && (
                                <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
                                    {selectedKeywords.map((keyword) => (
                                        <span
                                            key={keyword.id}
                                            className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                                        >
                                            {keyword.value}
                                            <button
                                                type="button"
                                                title={`Remove ${keyword.value}`}
                                                onClick={() =>
                                                    handleKeywordRemove(
                                                        keyword.id,
                                                    )
                                                }
                                                className="ml-2 text-blue-500 hover:text-blue-700"
                                            >
                                                <TbX className="w-3 h-3" />
                                            </button>
                                        </span>
                                    ))}
                                </div>
                            )}

                            {/* Search Input */}
                            <div className="relative">
                                <Input
                                    ref={searchInputRef}
                                    placeholder="Search keywords..."
                                    value={keywordSearchTerm}
                                    prefix={<TbSearch className="text-lg" />}
                                    onChange={(e) =>
                                        handleKeywordSearch(e.target.value)
                                    }
                                    onFocus={() => {
                                        if (keywordSearchTerm.trim()) {
                                            setShowSearchDropdown(true)
                                        }
                                    }}
                                />

                                {/* Search Results Dropdown */}
                                {showSearchDropdown && (
                                    <div
                                        ref={dropdownRef}
                                        className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                                    >
                                        {isSearchLoading ? (
                                            <div className="p-3 text-center text-gray-500">
                                                Searching...
                                            </div>
                                        ) : searchResults.length > 0 ? (
                                            searchResults.map((result) => (
                                                <button
                                                    key={result.id}
                                                    type="button"
                                                    className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none border-b border-gray-100 last:border-b-0"
                                                    onClick={() =>
                                                        handleKeywordSelect({
                                                            id: result.id,
                                                            value: result.value,
                                                        })
                                                    }
                                                >
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-gray-900">
                                                            {result.value}
                                                        </span>
                                                    </div>
                                                </button>
                                            ))
                                        ) : keywordSearchTerm.trim() ? (
                                            <div className="p-3 text-center text-gray-500">
                                                No keywords found
                                            </div>
                                        ) : null}
                                    </div>
                                )}
                            </div>

                            <p className="text-xs text-gray-500"></p>
                        </div>
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            {selectedKeywords.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                    {selectedKeywords.map((keyword) => (
                                        <span
                                            key={keyword.id}
                                            className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                                        >
                                            {keyword.value}
                                        </span>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-900">-</p>
                            )}
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                {isEditing && (
                    <div className="flex gap-3 pt-4 border-t border-gray-200">
                        <Button
                            loading={updateDocumentMutation.isPending}
                            className="flex-1"
                            onClick={handleSave}
                        >
                            <TbCheck className="w-4 h-4 mr-2" />
                            Save Changes
                        </Button>
                        <Button
                            variant="default"
                            disabled={updateDocumentMutation.isPending}
                            onClick={handleCancel}
                        >
                            <TbX className="w-4 h-4 mr-2" />
                            Cancel
                        </Button>
                    </div>
                )}
            </div>
        </Card>
    )
}

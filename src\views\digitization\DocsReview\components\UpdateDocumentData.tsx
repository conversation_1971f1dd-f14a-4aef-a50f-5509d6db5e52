import React, { useState, useEffect } from 'react'
import { Card, Button, Input, Select } from '@/components/ui'
import {
    TbEdit,
    TbCheck,
    TbX,
    TbCalendar,
    TbBuilding,
    TbNotes,
    TbTag,
} from 'react-icons/tb'
import { DocumentDetails, DocumentUpdate } from '@/@types/document'
import { useUpdateDocument } from '@/hooks/documents'
import { useGetOrgsStructure } from '@/hooks/orgs'
import { useKeywordSearch } from '@/hooks/keywords'

interface UpdateDocumentDataProps {
    document: DocumentDetails | null
    onUpdateSuccess?: () => void
}

export default function UpdateDocumentData({
    document,
    onUpdateSuccess,
}: UpdateDocumentDataProps) {
    const [isEditing, setIsEditing] = useState(false)
    const [formData, setFormData] = useState<DocumentUpdate>({
        organizationalNodeId: '',
        followUpDate: '',
        notes: '',
        keywordIds: [],
    })

    const updateDocumentMutation = useUpdateDocument()
    const { flattenedNodes } = useGetOrgsStructure()
        const { results: searchResults, isLoading: isSearchLoading, search } = useKeywordSearch()

    const nodesOptions = flattenedNodes.map((node) => ({
        value: node.code,
        label: `${node.name} (${node.code})`,
    }))

    // Add empty option as first choice
    const selectOptions = [
        { value: '', label: 'organization...' },
        ...nodesOptions,
    ]

    useEffect(() => {
        if (document) {
            setFormData({
                organizationalNodeId: document.organizationalNodeId || '',
                followUpDate: document.followUpDate || '',
                notes: document.notes || '',
                keywordIds: document.keywords?.map((k) => k.id) || [],
            })
        }
    }, [document])

    const handleInputChange = (
        field: keyof DocumentUpdate,
        value: string | number[],
    ) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }))
    }

    const handleSave = async () => {
        if (!document) return

        try {
            const updateData: DocumentUpdate = {
                ...formData,
                followUpDate: formData.followUpDate
                    ? new Date(formData.followUpDate).toISOString()
                    : '',
            }

            await updateDocumentMutation.mutateAsync({
                documentId: document?.documentId,
                document: updateData,
            })

            setIsEditing(false)
            onUpdateSuccess?.()
        } catch (error) {
            console.error('Update failed:', error)
        }
    }

    const handleCancel = () => {
        if (document) {
            setFormData({
                organizationalNodeId: document.organizationalNodeId || '',
                followUpDate: document.followUpDate || '',
                notes: document.notes || '',
                keywordIds: document.keywords?.map((k) => k.id) || [],
            })
        }
        setIsEditing(false)
    }

    const formatDate = (dateString: string) => {
        if (!dateString) return '-'
        return new Date(dateString).toLocaleDateString()
    }

    if (!document) {
        return (
            <Card className="p-6">
                <div className="text-center text-gray-500">
                    <TbEdit className="mx-auto text-4xl mb-2" />
                    <p>No document selected</p>
                </div>
            </Card>
        )
    }

    return (
        <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    Update Data
                </h3>
                {!isEditing && (
                    <Button
                        variant="default"
                        size="xs"
                        onClick={() => setIsEditing(true)}
                    >
                        <TbEdit className="w-4 h-4 mr-2" />
                    </Button>
                )}
            </div>

            <div className="space-y-6">
                {/* Organizational Node */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbBuilding className="w-4 h-4 mr-2" />
                        Organizational Node ID
                    </label>
                    {isEditing ? (
                        <Select
                            value={selectOptions.find(
                                (option) =>
                                    option.value ===
                                    formData.organizationalNodeId,
                            )}
                            options={selectOptions}
                            placeholder="Select an organization..."
                            onChange={(selectedOption) =>
                                handleInputChange(
                                    'organizationalNodeId',
                                    selectedOption?.value || '',
                                )
                            }
                            isClearable
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-gray-900">
                                {(() => {
                                    const selectedNode = flattenedNodes.find(
                                        (node) =>
                                            node.code ===
                                            formData.organizationalNodeId,
                                    )
                                    return selectedNode
                                        ? `${selectedNode.name} (${selectedNode.code})`
                                        : formData.organizationalNodeId || '-'
                                })()}
                            </p>
                        </div>
                    )}
                </div>

                {/* Follow Up Date */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbCalendar className="w-4 h-4 mr-2" />
                        Follow Up Date
                    </label>
                    {isEditing ? (
                        <Input
                            type="date"
                            value={
                                formData.followUpDate
                                    ? formData.followUpDate.split('T')[0]
                                    : ''
                            }
                            onChange={(e) =>
                                handleInputChange(
                                    'followUpDate',
                                    e.target.value,
                                )
                            }
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-gray-900">
                                {formatDate(formData.followUpDate)}
                            </p>
                        </div>
                    )}
                </div>

                {/* Notes */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbNotes className="w-4 h-4 mr-2" />
                        Notes
                    </label>
                    {isEditing ? (
                        <textarea
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical min-h-[100px]"
                            value={formData.notes}
                            placeholder="Enter notes..."
                            rows={4}
                            onChange={(e) =>
                                handleInputChange('notes', e.target.value)
                            }
                        />
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg min-h-[100px]">
                            <p className="text-gray-900 whitespace-pre-wrap">
                                {formData.notes || '-'}
                            </p>
                        </div>
                    )}
                </div>

                {/* Keywords */}
                <div className="space-y-2">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                        <TbTag className="w-4 h-4 mr-2" />
                        Keywords
                    </label>
                    {isEditing ? (
                        <div className="space-y-2">
                            <Input
                                placeholder="Enter keyword IDs (comma-separated)"
                                value={formData.keywordIds.join(', ')}
                                onChange={(e) => {
                                    const ids = e.target.value
                                        .split(',')
                                        .map((id) => parseInt(id.trim()))
                                        .filter((id) => !isNaN(id))
                                    handleInputChange('keywordIds', ids)
                                }}
                            />
                            <p className="text-xs text-gray-500">
                                Enter keyword IDs separated by commas (e.g., 1,
                                2, 3)
                            </p>
                        </div>
                    ) : (
                        <div className="p-3 bg-gray-50 rounded-lg">
                            {formData.keywordIds.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                    {formData.keywordIds.map((id) => (
                                        <span
                                            key={id}
                                            className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                                        >
                                            ID: {id}
                                        </span>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-900">-</p>
                            )}
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                {isEditing && (
                    <div className="flex gap-3 pt-4 border-t border-gray-200">
                        <Button
                            loading={updateDocumentMutation.isPending}
                            className="flex-1"
                            onClick={handleSave}
                        >
                            <TbCheck className="w-4 h-4 mr-2" />
                            Save Changes
                        </Button>
                        <Button
                            variant="default"
                            disabled={updateDocumentMutation.isPending}
                            onClick={handleCancel}
                        >
                            <TbX className="w-4 h-4 mr-2" />
                            Cancel
                        </Button>
                    </div>
                )}
            </div>
        </Card>
    )
}

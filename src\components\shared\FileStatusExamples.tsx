import { useState } from 'react'
import useTranslation from '@/utils/hooks/useTranslation'
import { Card } from '@/components/ui/Card'
import FileStatus from './FileStatus'
import FileStatusSelect from './FileStatusSelect'
import { getFileStatusConfig, FileStatus as FileStatusEnum } from '@/types/fileStatus'

/**
 * Example component demonstrating different ways to use the unified file status configuration
 * This component shows:
 * 1. Status display in different variants (badge, text, full)
 * 2. Status selection dropdown with different configurations
 * 3. Status configuration usage for custom implementations
 */
const FileStatusExamples = () => {
    const { t } = useTranslation()
    const [selectedStatus, setSelectedStatus] = useState('')
    const [selectedStatusWithAll, setSelectedStatusWithAll] = useState('')
    const [selectedStatusForLinking, setSelectedStatusForLinking] = useState('')
    const [selectedStatusForExport, setSelectedStatusForExport] = useState('')

    // Get the status configuration for custom usage
    const statusConfig = getFileStatusConfig(t)

    return (
        <div className="space-y-6 p-6">
            <h2 className="text-2xl font-bold mb-4">
                File Status Configuration Examples
            </h2>

            {/* Status Display Examples */}
            <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">
                    Status Display Variants
                </h3>
                <div className="space-y-4">
                    <div>
                        <h4 className="text-sm font-medium mb-2">Badge Variant:</h4>
                        <div className="flex gap-2 flex-wrap">
                            <FileStatus
                                status={FileStatusEnum.Open}
                                variant="badge"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndReadyToLinkedToBox}
                                variant="badge"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndLinkedToBox}
                                variant="badge"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndExported}
                                variant="badge"
                            />
                        </div>
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">Text Variant:</h4>
                        <div className="flex gap-4 flex-wrap">
                            <FileStatus
                                status={FileStatusEnum.Open}
                                variant="text"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndReadyToLinkedToBox}
                                variant="text"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndLinkedToBox}
                                variant="text"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndExported}
                                variant="text"
                            />
                        </div>
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Full Variant (with description):
                        </h4>
                        <div className="space-y-2">
                            <FileStatus
                                status={FileStatusEnum.Open}
                                variant="full"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndReadyToLinkedToBox}
                                variant="full"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndLinkedToBox}
                                variant="full"
                            />
                            <FileStatus
                                status={FileStatusEnum.ClosedAndExported}
                                variant="full"
                            />
                        </div>
                    </div>
                </div>
            </Card>

            {/* Status Selection Examples */}
            <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">
                    Status Selection Dropdowns
                </h3>
                <div className="space-y-4">
                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Basic Status Select:
                        </h4>
                        <FileStatusSelect
                            value={selectedStatus}
                            onChange={setSelectedStatus}
                            placeholder="Select a status..."
                            className="w-64"
                        />
                        {selectedStatus && (
                            <p className="text-sm text-gray-600 mt-1">
                                Selected: {selectedStatus}
                            </p>
                        )}
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Status Select with "All" Option:
                        </h4>
                        <FileStatusSelect
                            value={selectedStatusWithAll}
                            onChange={setSelectedStatusWithAll}
                            includeAll={true}
                            placeholder="Filter by status..."
                            className="w-64"
                        />
                        {selectedStatusWithAll && (
                            <p className="text-sm text-gray-600 mt-1">
                                Selected: {selectedStatusWithAll || 'All Statuses'}
                            </p>
                        )}
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Status Select for Linking (Filtered):
                        </h4>
                        <FileStatusSelect
                            value={selectedStatusForLinking}
                            onChange={setSelectedStatusForLinking}
                            variant="linking"
                            placeholder="Select files ready for linking..."
                            className="w-64"
                        />
                        {selectedStatusForLinking && (
                            <p className="text-sm text-gray-600 mt-1">
                                Selected: {selectedStatusForLinking}
                            </p>
                        )}
                    </div>

                    <div>
                        <h4 className="text-sm font-medium mb-2">
                            Status Select for Export (Filtered):
                        </h4>
                        <FileStatusSelect
                            value={selectedStatusForExport}
                            onChange={setSelectedStatusForExport}
                            variant="export"
                            placeholder="Select files ready for export..."
                            className="w-64"
                        />
                        {selectedStatusForExport && (
                            <p className="text-sm text-gray-600 mt-1">
                                Selected: {selectedStatusForExport}
                            </p>
                        )}
                    </div>
                </div>
            </Card>

            {/* Custom Implementation Example */}
            <Card className="p-4">
                <h3 className="text-lg font-semibold mb-4">
                    Custom Status Implementation
                </h3>
                <div className="space-y-2">
                    {Object.entries(statusConfig).map(([key, config]) => (
                        <div
                            key={key}
                            className="flex items-center justify-between p-3 border rounded-lg"
                        >
                            <div className="flex items-center gap-3">
                                <span
                                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}
                                >
                                    {config.label}
                                </span>
                                <div>
                                    <p className="font-medium">{config.label}</p>
                                    <p className="text-sm text-gray-500">
                                        {config.description}
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-sm font-mono">
                                    Value: {config.value}
                                </p>
                                <p className="text-sm text-gray-500">
                                    Color: {config.color}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
        </div>
    )
}

export default FileStatusExamples

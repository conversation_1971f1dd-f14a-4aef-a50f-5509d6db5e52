export enum FileStatus {
    Open = 0,
    ClosedAndReadyToLinkedToBox = 1,
    ClosedAndLinkedToBox = 2,
    ClosedAndExported = 3,
}

// Type definition for file status configuration
export interface FileStatusConfig {
    value: number
    label: string
    description: string
    className: string
    icon: string
    color: string
}

// Comprehensive file status configuration for both dropdowns and tables
export const getFileStatusConfig = (
    t: (key: string) => string,
): Record<FileStatus, FileStatusConfig> => ({
    [FileStatus.Open]: {
        value: 0,
        label: t('nav.files.status.open'),
        description: 'File is open and being processed',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: 'TbFolderOpen',
        color: 'blue',
    },
    [FileStatus.ClosedAndReadyToLinkedToBox]: {
        value: 1,
        label: t('nav.files.status.closedAndReadyToLinkedToBox'),
        description: 'File is closed and ready to be linked to a box',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: 'TbFolderCheck',
        color: 'yellow',
    },
    [FileStatus.ClosedAndLinkedToBox]: {
        value: 2,
        label: t('nav.files.status.closedAndLinkedToBox'),
        description: 'File is closed and linked to a box',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: 'TbFolderPlus',
        color: 'green',
    },
    [FileStatus.ClosedAndExported]: {
        value: 3,
        label: t('nav.files.status.closedAndExported'),
        description: 'File is closed and exported',
        className: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: 'TbFolderShare',
        color: 'purple',
    },
})

// Helper function to get status options for dropdowns
export const getFileStatusOptions = (
    t: (key: string) => string,
    includeAll: boolean = false,
) => {
    const statusConfig = getFileStatusConfig(t)
    const options = Object.values(statusConfig).map((config) => ({
        value: config.value.toString(),
        label: config.label,
    }))

    if (includeAll) {
        return [{ value: '', label: t('nav.shared.allStatuses') }, ...options]
    }

    return options
}

// Helper function to get status configuration by value
export const getFileStatusByValue = (
    status: number,
    t: (key: string) => string,
): FileStatusConfig | null => {
    const statusConfig = getFileStatusConfig(t)
    return statusConfig[status as FileStatus] || null
}

// Helper function to get status options for specific use cases
export const getFileStatusOptionsForLinking = (
    t: (key: string) => string,
) => {
    // Only show statuses that are relevant for linking to boxes
    return [
        {
            value: FileStatus.ClosedAndReadyToLinkedToBox.toString(),
            label: t('nav.files.status.closedAndReadyToLinkedToBox'),
        },
    ]
}

export const getFileStatusOptionsForExport = (
    t: (key: string) => string,
) => {
    // Only show statuses that are relevant for export
    return [
        {
            value: FileStatus.ClosedAndLinkedToBox.toString(),
            label: t('nav.files.status.closedAndLinkedToBox'),
        },
    ]
}
